from fastapi import APIRouter, Depends, Query
from database.client import get_db

from schemas.snapshot import SnapshotRead,SnapshotWithScore
from crud.snapshot import get_snapshots
from models.product_snapshots import ProductSnapshot,LatestProductSnapshot

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import Session

from typing import Optional, List

router = APIRouter(
    prefix="/snapshots",
    tags=["snapshots"]
)

@router.get("/", response_model=list[SnapshotRead])
async def list_snapshots(
    product_id: Optional[int] = Query(None),
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    if product_id is not None:
        return await get_snapshots(db, product_id=product_id, skip=skip, limit=limit)
    # 查詢所有商品的所有快照
    result = await db.execute(
        select(ProductSnapshot).offset(skip).limit(limit)
    )
    return result.scalars().all()


""" @router.get("/top", response_model=List[SnapshotWithScore])
def get_top_products(db: Session = Depends(get_db)):
    results = (
        db.query(LatestProductSnapshot)
        .order_by(LatestProductSnapshot.score.desc())
        .limit(10)
        .all()
    )
    return results """