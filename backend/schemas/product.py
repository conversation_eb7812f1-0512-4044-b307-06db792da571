# backend/schemas/product.py
from pydantic import BaseModel
from typing import Optional

class ProductBase(BaseModel):
    platform_id: str
    product_code: Optional[str]
    title: Optional[str]
    category_id: Optional[str]
    url: Optional[str]
    image_url: Optional[str]

class ProductRead(ProductBase):
    id: int
    price: Optional[float]

    class Config:
        orm_mode = True
        from_attributes = True

class ProductWithScore(ProductRead):
    score: float
