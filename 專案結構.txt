ecommerce-trend-analyzer/
├── frontend/                      # 前端應用（Next.js）
│   ├── src/
│   │   ├── app/
│   │   │   ├── layout.tsx            # 全域 layout（含 sidebar、header）
│   │   │   ├── page.tsx              # 首頁：熱門商品排行榜
│   │   │   ├── product/[id]/         # 單一商品分析頁
│   │   │   ├── keywords/             # 熱門關鍵字分析頁
│   │   │   ├── categories/           # 類別分析頁
│   │   │   └── admin/                # 後台管理頁
│   │   ├── components/                    # 共用元件
│   │   │   ├── Sidebar.tsx
│   │   │   ├── Header.tsx
│   │   │   ├── ProductRankingChart.tsx     # 長條圖（熱門商品排行榜),首頁
│   │   │   ├── ProductReviewTrendChart.tsx # 折線圖(熱度變化趨勢),首頁
│   │   │   ├── ProductCard.tsx             # 首頁與類別分析頁
│   │   │   ├── TrendChart.tsx              # 折線圖（單商品價格趨勢）,單一商品分析頁
│   │   │   ├── KeywordChart.tsx            # 柱狀圖（熱門關鍵字）,熱門關鍵字分析頁
│   │   │   ├── CategoryBarChart.tsx        # 堆疊長條圖(分類商品成長趨勢),類別分析頁
│   │   │   ├── ProductOutlierChart.tsx     # 折線圖 + 異常點(商品異常價格波動),單一商品分析頁
│   │   │   ├── KeywordHeatMap.tsx          # 熱力圖或雷達圖(熱門關鍵字熱力圖),熱門關鍵字分析頁
│   │   │   └── PriceBox.tsx                # 小統計卡片(價格統計卡片),單一商品頁頭部
            CrawlLogTable.tsx
            ProductRankingChart.tsx
            temp.tsx
│   │   ├── lib/                      # 資料取得邏輯（fetch 封裝）
│   │   │   ├──api/   
│   │   │   │   ├── index.ts                #統一輸出
│   │   │   │   ├── categories.ts
│   │   │   │   ├── admin.ts
│   │   │   │   ├── keyword.ts
│   │   │   │   └── product.ts
│   │   ├── types/                    # TypeScript 共用檔案
│   │   │   ├── index.ts                    #統一輸出                  
│   │   │   ├── product.ts
│   │   │   ├── category.ts
│   │   │   ├── keyword.ts
│   │   │   ├── admin.ts
│   │   │   └── crawl.ts
│   ├── public/                   # 靜態資源（圖片、圖示等）
│   ├── styles/
│   │   └── globals.css
│   ├── tailwind.config.ts
│   ├── tsconfig.json
│   ├── .env.local
│   └── package.json
│
├── backend/                      # 後端 API（FastAPI）
│   ├── .env                         # 資料庫與服務環境變數
│   ├── config.py                    # 設定管理（環境變數、DB 連線等）
│   ├── exceptions.py                # 自訂例外與統一錯誤處理
│   ├── main.py                      # FastAPI 主入口
│   ├── requirements.txt             # 生產依賴
│   ├── requirements-dev.txt         # 開發依賴（pytest, coverage 等）
│   ├── auth/                        # 權限驗證（JWT、OAuth 等）
│   │   └── ...                      # 權限相關模組
│   ├── database/                    # 資料庫連線與操作
│   │   └── client.py                # SQLAlchemy 連線與 session 管理
│   ├── logs/                        # 日誌輸出（log 檔案集中管理）
│   │   └── ...                      # log 檔案或 log utility
│   ├── middleware/                  # 中介軟體（CORS、請求日誌等）
│   │   └── ...                      # middleware 實作
│   ├── migrations/                  # 資料庫 schema 遷移（Alembic 等）
│   │   └── ...                      # 不要手動在 Supabase 建表，全部用 Alembic migration 管理
                                    migration script
│   ├── models/                      # ORM 資料模型
│   │   ├── __init__.py              # 匯出所有 model
│   │   ├── product.py               # 商品主表
│   │   ├── product_snapshots.py     # 商品快照表
│   │   ├── categories.py            # 商品分類表
│   │   ├── platform.py              # 平台表
│   │   ├── keywords.py              # 熱門關鍵字表
│   │   ├── crawl_logs.py            # 爬蟲紀錄表
│   │   └── ...                      # 其他模型
│   ├── routers/                     # API 路由
│   │   ├── __init__.py
│   │   ├── products.py              # 商品相關 API
│   │   ├── categories.py            # 分類相關 API
│   │   ├── keywords.py              # 關鍵字相關 API
│   │   ├── admin.py                 # 後台管理 API
│   │   └── ...                      # 其他路由
│   ├── schemas/                     # Pydantic schema（資料驗證與序列化）
│   │   └── ...                      # 各資源 schema
│   ├── services/                    # 商業邏輯（分析、計算等）
│   │   └── trend_analysis.py        # 趨勢分析服務
│   │   └── ...                      # 其他服務
│   ├── tests/                       # 測試，可細分 unit/、integration/
│   │   └── ...                      # 測試檔案
│   ├── utils/                       # 工具程式
│   │   └── ...                      # 共用工具
│   └── README.md                 # 專案說明文件
│
├── shared/                       # 前後端共用 schema 或常數
│   ├── types.ts                  # 共用 TypeScript 類型定義
│   ├── constants.ts              # 平台定義、分類表等
│   └── interfaces/               # 若使用 Swagger/OpenAPI 可共用
│
├── n8n/                          # 爬蟲流程設定與備份
│   ├── momo-workflow.json
│   ├── shopee-workflow.json
│   ├── amazon-workflow.json
│   └── README.md                 # 說明如何運作與部署
│
├── docker-compose.yml           # 部署組合（可包含 FastAPI + n8n + DB）
├── .env                         # 環境變數（如 Supabase、API Key）
└── LICENSE
