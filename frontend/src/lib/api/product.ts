// lib/api/products.ts
import type {
  Product,
  ProductSnapshot,
  ProductWithScore
} from "@/types/product";
const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

// 單一商品價格快照（折線圖）
export async function fetchProductTrend(id: number): Promise<ProductSnapshot[]> {
  const res = await fetch(`${BASE_URL}/products/${id}/trend`);
  if (!res.ok) throw new Error("無法取得商品趨勢");
  return res.json();
}

// 商品詳細資料

/* export async function fetchProductDetail(id: number): Promise<ProductDetail> {
  const res = await fetch(`${BASE_URL}/products/${id}`);
  if (!res.ok) throw new Error("無法取得商品資料");
  return res.json();
} */



// 熱門商品排行榜
/* export async function fetchTopProducts(): Promise<Product[]> {
  const res = await fetch(`${BASE_URL}/products/top`);
  if (!res.ok) throw new Error("無法取得熱門商品排行榜");
  return res.json();
} */

// 快速成長商品排行榜
export async function fetchFastestGrowingProducts(): Promise<Product[]> {
  const res = await fetch(`${BASE_URL}/products/fastest-growing`);
  if (!res.ok) throw new Error("無法取得快速成長商品排行榜");
  return res.json();
}


interface TopProductParams {
  limit?: number;
  rating_weight?: number;
  review_count_weight?: number;
  price_penalty?: number;
}

export async function fetchTopProducts(params: TopProductParams = {}): Promise<ProductWithScore[]> {
  const query = new URLSearchParams(params as any).toString();
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/top?${query}`);
  if (!res.ok) throw new Error("無法取得熱門商品排行榜");
  return res.json();
}

