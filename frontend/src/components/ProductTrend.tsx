"use client";

import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

export interface TrendPoint {
  date: string;
  price: number;
}

export default function ProductTrend({ data }: { data: TrendPoint[] }) {
  return (
    <div className="bg-white p-4 rounded-xl shadow w-full">
      <h2 className="text-lg font-bold mb-4">價格變化趨勢</h2>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey="price" stroke="#8884d8" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
