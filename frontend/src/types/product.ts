import { StaticImport } from "next/dist/shared/lib/get-img-props";

// types/product.ts
export interface Product {
  id: number;
  title: string; // 後端回傳 title
  platform_id: string;
  category_id: string;
  url?: string;
  image_url?: string | StaticImport; // 支援 string 及 StaticImport
  snapshots?: ProductSnapshot[]; // 新增快照欄位
  // 前端顯示用
  name?: string;
  platform?: string;
  category?: string;
  price?: number;
  avgPrice?: number;
  score?: number; // 後端回傳的熱門分數
}

export interface ProductSnapshot {
  id?: number;
  product_id?: number;
  price: number;
  rating?: string;
  review_count?: number;
  rank?: string;
  created_at?: string;
  captured_at?: string;
  score: number;
}


export interface ProductWithScore {
  id: number;
  title: string;
  platform_id: string;
  category_id: string;
  url?: string;
  image_url?: string | StaticImport;

  // 來自 snapshot 的欄位
  price: number;
  rating?: string;
  review_count?: number;
  rank?: string;
  score: number;
  captured_at?: string;

  // 顯示用（optional）
  name?: string;
  platform?: string;
  category?: string;
}

